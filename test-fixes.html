<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Async Data Processing Fixes</title>
    <style>
        body {
            font-family: 'Amazon Ember', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8f9fa;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 14px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1.5px solid #e9ecef;
            border-radius: 14px;
            position: relative;
        }
        
        .test-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 16px;
        }
        
        .test-button {
            background: #470CED;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 12px;
            margin-bottom: 12px;
        }
        
        .test-button:hover {
            background: #3a0bc4;
        }
        
        .test-results {
            background: #f8f9fa;
            padding: 16px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            margin-top: 16px;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .chart-container {
            width: 100%;
            height: 200px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 14px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            position: relative;
        }
        
        .sales-card-container {
            width: 100%;
            height: 150px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 14px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            position: relative;
        }
    </style>
    
    <!-- Load required dependencies -->
    <link rel="stylesheet" href="./snapapp.css">
    <script src="./snapapp.js"></script>
    <script src="./components/snap-timezone/snap-timezone.js"></script>
</head>
<body>
    <div class="test-container">
        <h1>🔧 Async Data Processing Fixes Test</h1>
        <p>Testing the fixes for Last Week Chart visibility, Sales Cards loading, and Loader overlay styling.</p>
        
        <div class="test-section">
            <div class="test-title">1. Loader Overlay Styling Test</div>
            <div class="chart-container" id="test-chart-container">
                Chart Container (should have rounded loader overlay)
            </div>
            <button class="test-button" onclick="testLoaderStyling()">Test Loader Overlay</button>
            <div class="test-results" id="loader-results"></div>
        </div>
        
        <div class="test-section">
            <div class="test-title">2. Async Data Generation Test</div>
            <div class="sales-card-container" id="test-sales-container">
                Sales Card Container
            </div>
            <button class="test-button" onclick="testAsyncDataGeneration()">Test Async Data Generation</button>
            <div class="test-results" id="async-results"></div>
        </div>
        
        <div class="test-section">
            <div class="test-title">3. Chart Data Structure Test</div>
            <button class="test-button" onclick="testChartDataStructure()">Test Chart Data Structure</button>
            <div class="test-results" id="chart-data-results"></div>
        </div>
        
        <div class="test-section">
            <div class="test-title">4. Progress Indicator Test</div>
            <button class="test-button" onclick="testProgressIndicators()">Test Progress Indicators</button>
            <div class="test-results" id="progress-results"></div>
        </div>
    </div>
    
    <script>
        // Test functions
        
        function testLoaderStyling() {
            const container = document.getElementById('test-chart-container');
            const results = document.getElementById('loader-results');
            
            results.textContent = 'Testing loader overlay styling...\n';
            
            // Ensure container has proper positioning
            container.style.position = 'relative';
            container.style.borderRadius = '14px';
            
            // Show loader overlay
            if (window.SnapLoader) {
                window.SnapLoader.showOverlay(container, {
                    text: 'Testing rounded corners...',
                    id: 'test-loader'
                });
                
                results.textContent += '✅ Loader overlay shown\n';
                results.textContent += '✅ Container has position: relative\n';
                results.textContent += '✅ Container has border-radius: 14px\n';
                results.textContent += '✅ Overlay should inherit rounded corners\n';
                
                // Hide after 3 seconds
                setTimeout(() => {
                    window.SnapLoader.hideOverlay('test-loader');
                    results.textContent += '✅ Loader overlay hidden\n';
                }, 3000);
            } else {
                results.textContent += '❌ SnapLoader not available\n';
            }
        }
        
        async function testAsyncDataGeneration() {
            const container = document.getElementById('test-sales-container');
            const results = document.getElementById('async-results');
            
            results.textContent = 'Testing async data generation...\n';
            
            try {
                // Test async data generation
                const startTime = performance.now();
                
                // Show progress indicator
                container.style.position = 'relative';
                container.style.borderRadius = '14px';
                
                if (window.SnapLoader) {
                    window.SnapLoader.showOverlay(container, {
                        text: 'Generating test data...',
                        id: 'test-async-loader'
                    });
                }
                
                // Simulate async data generation
                const data = await generateTestDataAsync();
                const endTime = performance.now();
                
                // Hide progress indicator
                if (window.SnapLoader) {
                    window.SnapLoader.hideOverlay('test-async-loader');
                }
                
                results.textContent += `✅ Async data generation completed in ${Math.round(endTime - startTime)}ms\n`;
                results.textContent += `✅ Generated ${data.length} data points\n`;
                results.textContent += `✅ Data structure: ${JSON.stringify(data[0], null, 2)}\n`;
                
                container.textContent = `✅ Data Generated: ${data.length} items`;
                
            } catch (error) {
                results.textContent += `❌ Error: ${error.message}\n`;
                
                // Hide progress indicator on error
                if (window.SnapLoader) {
                    window.SnapLoader.hideOverlay('test-async-loader');
                }
            }
        }
        
        async function generateTestDataAsync() {
            return new Promise((resolve) => {
                setTimeout(() => {
                    const data = [];
                    for (let i = 0; i < 7; i++) {
                        data.push({
                            day: i,
                            sales: Math.floor(Math.random() * 100),
                            returns: Math.floor(Math.random() * 10),
                            marketplaces: ['US', 'UK', 'DE'].map(code => ({
                                code,
                                sales: Math.floor(Math.random() * 50),
                                returns: Math.floor(Math.random() * 5)
                            }))
                        });
                    }
                    resolve(data);
                }, 1000); // Simulate 1 second processing time
            });
        }
        
        async function testChartDataStructure() {
            const results = document.getElementById('chart-data-results');
            
            results.textContent = 'Testing chart data structure...\n';
            
            try {
                // Test the data structure that charts expect
                const testData = {
                    month: 'JAN',
                    day: '01',
                    year: '25',
                    marketplaces: [
                        { code: 'US', sales: 50, royalties: 17, returns: 2 },
                        { code: 'UK', sales: 30, royalties: 10, returns: 1 },
                        { code: 'DE', sales: 25, royalties: 8, returns: 0 }
                    ],
                    sales: 105,
                    royalties: 35,
                    returns: 3,
                    values: [50, 30, 25],
                    labels: ['US', 'UK', 'DE']
                };
                
                results.textContent += '✅ Chart data structure test passed\n';
                results.textContent += `✅ Data format: ${JSON.stringify(testData, null, 2)}\n`;
                
                // Test async wrapper
                const asyncData = await testAsyncWrapper(testData);
                results.textContent += '✅ Async wrapper test passed\n';
                results.textContent += `✅ Async result: ${JSON.stringify(asyncData, null, 2)}\n`;
                
            } catch (error) {
                results.textContent += `❌ Error: ${error.message}\n`;
            }
        }
        
        async function testAsyncWrapper(data) {
            return new Promise((resolve) => {
                setTimeout(() => {
                    resolve(data);
                }, 100);
            });
        }
        
        async function testProgressIndicators() {
            const results = document.getElementById('progress-results');
            
            results.textContent = 'Testing progress indicators...\n';
            
            try {
                // Test multiple progress indicators
                const containers = [
                    document.getElementById('test-chart-container'),
                    document.getElementById('test-sales-container')
                ];
                
                for (let i = 0; i < containers.length; i++) {
                    const container = containers[i];
                    const loaderId = `test-progress-${i}`;
                    
                    container.style.position = 'relative';
                    container.style.borderRadius = '14px';
                    
                    if (window.SnapLoader) {
                        window.SnapLoader.showOverlay(container, {
                            text: `Loading step ${i + 1}...`,
                            id: loaderId
                        });
                        
                        results.textContent += `✅ Progress indicator ${i + 1} shown\n`;
                        
                        // Wait 2 seconds then hide
                        await new Promise(resolve => setTimeout(resolve, 2000));
                        
                        window.SnapLoader.hideOverlay(loaderId);
                        results.textContent += `✅ Progress indicator ${i + 1} hidden\n`;
                    }
                }
                
                results.textContent += '✅ All progress indicators test completed\n';
                
            } catch (error) {
                results.textContent += `❌ Error: ${error.message}\n`;
            }
        }
        
        // Initialize tests
        window.addEventListener('load', () => {
            console.log('🧪 Async Data Processing Fixes Test Ready');
            
            // Check if SnapLoader is available
            if (window.SnapLoader) {
                console.log('✅ SnapLoader available');
            } else {
                console.log('❌ SnapLoader not available');
            }
        });
    </script>
</body>
</html>
