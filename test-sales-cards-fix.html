<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Sales Cards Loading Fix</title>
    <style>
        body {
            font-family: 'Amazon Ember', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8f9fa;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 14px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1.5px solid #e9ecef;
            border-radius: 14px;
            position: relative;
        }
        
        .test-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 16px;
        }
        
        .test-button {
            background: #470CED;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 12px;
            margin-bottom: 12px;
        }
        
        .test-button:hover {
            background: #3a0bc4;
        }
        
        .test-results {
            background: #f8f9fa;
            padding: 16px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            margin-top: 16px;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .mock-four-sales-cards-section {
            width: 100%;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 14px;
            padding: 20px;
            margin: 16px 0;
            position: relative;
        }
        
        .mock-sales-card {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 16px;
            margin: 8px 0;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-success { background: #28a745; }
        .status-error { background: #dc3545; }
        .status-warning { background: #ffc107; }
    </style>
    
    <!-- Load required dependencies -->
    <link rel="stylesheet" href="./snapapp.css">
    <script src="./snapapp.js"></script>
</head>
<body>
    <div class="test-container">
        <h1>🔧 Sales Cards Loading Fix Test</h1>
        <p>Testing the fix for infinite loading issue in sales cards.</p>
        
        <div class="test-section">
            <div class="test-title">1. Mock Four Sales Cards Container</div>
            <div class="mock-four-sales-cards-section four-sales-cards-section">
                <div class="mock-sales-card Sales-card-div">Mock Sales Card 1</div>
                <div class="mock-sales-card Sales-card-div">Mock Sales Card 2</div>
                <div class="mock-sales-card Sales-card-div">Mock Sales Card 3</div>
                <div class="mock-sales-card Sales-card-div">Mock Sales Card 4</div>
            </div>
            <button class="test-button" onclick="testWithMockCards()">Test With Mock Cards</button>
            <div class="test-results" id="mock-results"></div>
        </div>
        
        <div class="test-section">
            <div class="test-title">2. Empty Container Test</div>
            <div class="mock-four-sales-cards-section four-sales-cards-section" id="empty-container">
                <!-- No sales cards here -->
            </div>
            <button class="test-button" onclick="testWithEmptyContainer()">Test With Empty Container</button>
            <div class="test-results" id="empty-results"></div>
        </div>
        
        <div class="test-section">
            <div class="test-title">3. Error Handling Test</div>
            <button class="test-button" onclick="testErrorHandling()">Test Error Handling</button>
            <div class="test-results" id="error-results"></div>
        </div>
        
        <div class="test-section">
            <div class="test-title">4. DOM Query Test</div>
            <button class="test-button" onclick="testDOMQueries()">Test DOM Queries</button>
            <div class="test-results" id="dom-results"></div>
        </div>
    </div>
    
    <script>
        // Mock the async data generation function
        async function generateFourSalesCardsMockDataAsync() {
            return new Promise((resolve) => {
                setTimeout(() => {
                    resolve({
                        currentMonth: { totalSales: 1000 },
                        lastMonth: { totalSales: 1200 },
                        currentYear: { totalSales: 15000 },
                        lastYear: { totalSales: 18000 }
                    });
                }, 500);
            });
        }
        
        // Mock the apply function with the fixed error handling
        async function testApplyFourSalesCardsMockData(containerSelector = '.four-sales-cards-section') {
            console.log('📊 Testing mock data application...');

            // Show progress indicator for data generation
            const fourSalesCardsSection = document.querySelector(containerSelector);
            if (fourSalesCardsSection && window.SnapLoader) {
                // Ensure container has proper positioning for overlay
                fourSalesCardsSection.style.position = 'relative';
                fourSalesCardsSection.style.borderRadius = '14px';

                window.SnapLoader.showOverlay(fourSalesCardsSection, {
                    text: 'Loading sales data...',
                    id: 'test-four-sales-cards-loader'
                });
            }

            try {
                const mockData = await generateFourSalesCardsMockDataAsync();

                // Debug: Check if the container exists
                console.log('🔍 Debugging four sales cards container:', {
                    fourSalesCardsSection: !!fourSalesCardsSection,
                    containerExists: !!document.querySelector(containerSelector),
                    allSalesCards: document.querySelectorAll('.Sales-card-div').length,
                    fourSalesCardsSpecific: document.querySelectorAll(`${containerSelector} .Sales-card-div`).length
                });

                // Get all four sales cards in the four-sales-cards-section
                const fourSalesCards = document.querySelectorAll(`${containerSelector} .Sales-card-div`);
                console.log(`📊 Found ${fourSalesCards.length} four sales cards`);

                if (fourSalesCards.length === 0) {
                    console.error('❌ No four sales cards found! Check if container exists');
                    
                    // Hide progress indicator even on error
                    if (fourSalesCardsSection && window.SnapLoader) {
                        window.SnapLoader.hideOverlay('test-four-sales-cards-loader');
                    }
                    
                    return { success: false, error: 'No sales cards found' };
                }

                // Simulate processing the cards
                fourSalesCards.forEach((card, index) => {
                    card.textContent = `Updated Sales Card ${index + 1} - Sales: ${Object.values(mockData)[index]?.totalSales || 0}`;
                });

                console.log('✅ Four sales cards mock data applied successfully');
                return { success: true, cardsFound: fourSalesCards.length };
                
            } catch (error) {
                console.error('❌ Error applying four sales cards mock data:', error);
                return { success: false, error: error.message };
            } finally {
                // Always hide progress indicator, regardless of success or failure
                if (fourSalesCardsSection && window.SnapLoader) {
                    window.SnapLoader.hideOverlay('test-four-sales-cards-loader');
                }
            }
        }
        
        // Test functions
        async function testWithMockCards() {
            const results = document.getElementById('mock-results');
            results.textContent = 'Testing with mock cards...\n';
            
            try {
                const result = await testApplyFourSalesCardsMockData('.four-sales-cards-section');
                
                if (result.success) {
                    results.textContent += `✅ Test passed! Found ${result.cardsFound} cards\n`;
                    results.textContent += '✅ Loader should have appeared and disappeared\n';
                    results.textContent += '✅ Cards should be updated with mock data\n';
                } else {
                    results.textContent += `❌ Test failed: ${result.error}\n`;
                }
                
            } catch (error) {
                results.textContent += `❌ Test error: ${error.message}\n`;
            }
        }
        
        async function testWithEmptyContainer() {
            const results = document.getElementById('empty-results');
            results.textContent = 'Testing with empty container...\n';
            
            try {
                const result = await testApplyFourSalesCardsMockData('#empty-container');
                
                if (!result.success && result.error === 'No sales cards found') {
                    results.textContent += '✅ Test passed! Correctly handled empty container\n';
                    results.textContent += '✅ Loader should have appeared and disappeared\n';
                    results.textContent += '✅ No infinite loading state\n';
                } else {
                    results.textContent += `❌ Test failed: Expected error for empty container\n`;
                }
                
            } catch (error) {
                results.textContent += `❌ Test error: ${error.message}\n`;
            }
        }
        
        async function testErrorHandling() {
            const results = document.getElementById('error-results');
            results.textContent = 'Testing error handling...\n';
            
            try {
                // Test with non-existent container
                const result = await testApplyFourSalesCardsMockData('.non-existent-container');
                
                results.textContent += '✅ Error handling test completed\n';
                results.textContent += '✅ No infinite loading state\n';
                results.textContent += `✅ Result: ${JSON.stringify(result, null, 2)}\n`;
                
            } catch (error) {
                results.textContent += `❌ Test error: ${error.message}\n`;
            }
        }
        
        function testDOMQueries() {
            const results = document.getElementById('dom-results');
            results.textContent = 'Testing DOM queries...\n';
            
            try {
                const queries = {
                    'All .Sales-card-div': document.querySelectorAll('.Sales-card-div').length,
                    '.four-sales-cards-section': document.querySelectorAll('.four-sales-cards-section').length,
                    '.four-sales-cards-section .Sales-card-div': document.querySelectorAll('.four-sales-cards-section .Sales-card-div').length,
                    '#empty-container': document.querySelectorAll('#empty-container').length,
                    '#empty-container .Sales-card-div': document.querySelectorAll('#empty-container .Sales-card-div').length
                };
                
                Object.entries(queries).forEach(([selector, count]) => {
                    results.textContent += `${selector}: ${count} elements\n`;
                });
                
                results.textContent += '\n✅ DOM query test completed\n';
                
            } catch (error) {
                results.textContent += `❌ DOM query error: ${error.message}\n`;
            }
        }
        
        // Initialize tests
        window.addEventListener('load', () => {
            console.log('🧪 Sales Cards Loading Fix Test Ready');
            
            // Check if SnapLoader is available
            if (window.SnapLoader) {
                console.log('✅ SnapLoader available');
            } else {
                console.log('❌ SnapLoader not available');
            }
            
            // Run DOM query test automatically
            testDOMQueries();
        });
    </script>
</body>
</html>
